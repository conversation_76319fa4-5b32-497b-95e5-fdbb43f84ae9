## 本说明是Air780E  GSM模块的AT指令说明文档，请根据文档编制代码
## 下面的AT指令测试是通过串口助手连接模块实测数据
## AT指令收到的信息都打印输出，输出信息使用英文，中文会乱码的
## AT指令发送后 while等待接收回复，如果超时 打印输出AT指令+ERROR 表示这条指令设置失败了
## GSM模块与单品硬件连接使用LPUART1串口 波特率 115200
## 在E:\BaiduSyncdisk\01BLWK_PROJETR\01cubeMX\05Camera_RF\Camera_CAT1\Core\Src里新建GSM.c文件
## 在E:\BaiduSyncdisk\01BLWK_PROJETR\01cubeMX\05Camera_RF\Camera_CAT1\Core\Inc里新建GSM.h文件
## 代码中文注释 打印输出使用英文
##################################################

[13:39:03.378]收←◆^boot.rom'v\0\0\0'!\n  //GSM模块上电
[13:39:04.404]收←◆
RDY

[13:39:06.342]收←◆
^MODE: 17,17

+E_UTRAN Service

+CGEV: ME PDN ACT 1

+NITZ: 2025/07/15,05:39:06+0,0

[13:39:15.893]发→◇ATE0  //发送回显关闭指令 第一次会回显 ATE0 之后只有 OK
□
[13:39:15.898]收←◆ATE0

OK

[13:39:18.005]发→◇AT+CGMM //获取模块型号
□
[13:39:18.012]收←◆
+CGMM: "Air780EG"

OK

[13:39:18.774]发→◇AT+CCID //获取CCID号
□
[13:39:18.781]收←◆
89860316249511500582

OK

[13:39:19.814]发→◇AT+CBC //获取模块电压
□
[13:39:19.821]收←◆
+CBC: 3483

OK

[13:39:26.374]发→◇AT+CSQ //获取信号强度
□
[13:39:26.381]收←◆
+CSQ: 25,0

OK

[13:39:28.710]发→◇AT+CIPSTART="TCP","************",48085 //连接TCP服务器
□
[13:39:28.721]收←◆
OK

[13:39:28.851]收←◆
CONNECT OK

[13:39:28.878]收←◆


[13:39:31.678]发→◇AT+CIPQSEND=1 //设置非透传快发模式
□
[13:39:31.685]收←◆
OK

[13:39:35.855]发→◇AT+CIPSEND=122
□
[13:39:35.863]收←◆
>
[14:05:50.956]发→◇AT+CIPSTART="TCP","************",48085 //连接TCP服务器
□
[14:05:50.966]收←◆
OK

[14:05:51.083]收←◆
CONNECT OK



[14:05:53.188]发→◇AT+CIPQSEND=1 //设置非透传快发模式
□
[14:05:53.195]收←◆
OK

[14:05:56.172]发→◇AT+CIPSEND=122
□
[14:05:56.179]收←◆
>
[14:05:57.131]发→◇HY105S+12345.70898+3016.57080+052857.270625+38.9+1+6+1.7+4.20+55.6+2.9+3.0+3.8+-1.9+0.0+0.00+-128+89860316249511500582+E
□
[14:05:57.146]收←◆
DATA ACCEPT:122

[14:05:57.200]收←◆ZL+S30+F00E00+N0


